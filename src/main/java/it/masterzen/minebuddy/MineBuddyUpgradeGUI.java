package it.masterzen.minebuddy;

import it.masterzen.MongoDB.PlayerData;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.commands.Main;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI class for MineBuddy upgrade system
 * Handles the upgrade interface and player interactions
 */
public class MineBuddyUpgradeGUI implements Listener {
    
    private final AlphaBlockBreak plugin;
    private final MineBuddyUpgradeManager upgradeManager;
    private final String prefix = "§e§lMINE BUDDY §8»§7 ";
    
    public MineBuddyUpgradeGUI(AlphaBlockBreak plugin, MineBuddyUpgradeManager upgradeManager) {
        this.plugin = plugin;
        this.upgradeManager = upgradeManager;
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && 
            event.getClickedInventory().equals(event.getView().getTopInventory())) {
            
            Player player = (Player) event.getWhoClicked();
            
            if (event.getView().getTitle().equalsIgnoreCase("§e§lMINE BUDDY §f| §7Upgrades")) {
                event.setCancelled(true);
                
                if (event.getCurrentItem() != null && event.getCurrentItem().hasItemMeta()) {
                    ItemStack clickedItem = event.getCurrentItem();
                    String displayName = clickedItem.getItemMeta().getDisplayName();
                    
                    MineBuddy.LocationType locationType = null;
                    
                    // Determine which upgrade was clicked
                    if (event.getSlot() == 11) {
                        locationType = MineBuddy.LocationType.MINE;
                    } else if (event.getSlot() == 13) {
                        locationType = MineBuddy.LocationType.FARMER_WARP;
                    } else if (event.getSlot() == 15) {
                        locationType = MineBuddy.LocationType.DUNGEON;
                    }

                    if (locationType != null) {
                        // Handle upgrade purchase
                        if (upgradeManager.purchaseUpgrade(player, locationType)) {
                            // Refresh the GUI to show updated information
                            openGUI(player);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Opens the MineBuddy upgrade GUI for a player
     * 
     * @param player The player to open the GUI for
     */
    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lMINE BUDDY §f| §7Upgrades");
        Main.FillBorder(gui);
        
        PlayerData playerData = plugin.getMongoReader().getPlayerData(player.getUniqueId());
        if (playerData == null) {
            player.sendMessage(prefix + "§cError: Could not load your data!");
            return;
        }
        
        // Create upgrade items for each location type
        ItemStack mineUpgrade = createUpgradeItem(player, playerData, MineBuddy.LocationType.MINE);
        ItemStack farmerUpgrade = createUpgradeItem(player, playerData, MineBuddy.LocationType.FARMER_WARP);
        ItemStack dungeonUpgrade = createUpgradeItem(player, playerData, MineBuddy.LocationType.DUNGEON);
        
        // Place items in the GUI
        gui.setItem(11, mineUpgrade);      // Left position
        gui.setItem(13, farmerUpgrade);    // Center position
        gui.setItem(15, dungeonUpgrade);   // Right position
        
        // Add player stats item
        ItemStack statsItem = createStatsItem(player, playerData);
        gui.setItem(22, statsItem);
        
        player.openInventory(gui);
    }
    
    /**
     * Creates an upgrade item for a specific location type
     * 
     * @param player The player
     * @param playerData The player's data
     * @param locationType The location type
     * @return The upgrade item
     */
    private ItemStack createUpgradeItem(Player player, PlayerData playerData, MineBuddy.LocationType locationType) {
        Material material = getUpgradeMaterial(locationType);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        // Set display name
        String locationName = upgradeManager.getLocationDisplayName(locationType);
        meta.setDisplayName(locationName + " §7Upgrade");
        
        // Create lore
        List<String> lore = new ArrayList<>();
        lore.add("");
        
        // Current level info
        int currentLevel = upgradeManager.getCurrentUpgradeLevel(playerData, locationType);
        double currentMultiplier = upgradeManager.calculateBonusMultiplier(playerData, locationType);
        String bonusPercentage = upgradeManager.getBonusPercentageDisplay(locationType);
        
        lore.add("§7§lCURRENT LEVEL");
        lore.add("§7| §fLevel: §a" + currentLevel);
        lore.add("§7| §fBonus: §a+" + String.format("%.0f", (currentMultiplier - 1.0) * 100) + "%");
        lore.add("§7| §fPer Level: §a+" + bonusPercentage);
        lore.add("");
        
        // Next level info
        lore.add("§6§lNEXT LEVEL");
        lore.add("§6| §fLevel: §e" + (currentLevel + 1));
        double nextMultiplier = upgradeManager.calculateBonusMultiplier(playerData, locationType) + 
                               (locationType == MineBuddy.LocationType.MINE ? 0.20 : 0.10);
        lore.add("§6| §fBonus: §e+" + String.format("%.0f", (nextMultiplier - 1.0) * 100) + "%");
        lore.add("");
        
        // Price info
        long upgradePrice = upgradeManager.calculateUpgradePrice(player, locationType);
        long playerBlocks = playerData.getBlocksMined() != null ? playerData.getBlocksMined() : 0;
        boolean canAfford = upgradeManager.canPurchaseUpgrade(player, locationType);
        
        lore.add("§e§lPRICE");
        lore.add("§e| §f" + plugin.newFormatNumber(upgradePrice, player) + " §7Blocks");
        lore.add("");
        
        // Status
        lore.add("§7§lSTATUS");
        if (canAfford) {
            lore.add("§a| §fClick to purchase!");
        } else {
            lore.add("§c| §fNot enough blocks!");
            lore.add("§c| §fNeed: §f" + plugin.newFormatNumber(upgradePrice - playerBlocks, player) + " §7more");
        }
        lore.add("");
        
        meta.setLore(lore);
        
        // Add enchantment effect if affordable
        if (canAfford) {
            plugin.addGlowing(meta);
        }
        
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * Creates a stats item showing player's current blocks and overall progress
     * 
     * @param player The player
     * @param playerData The player's data
     * @return The stats item
     */
    private ItemStack createStatsItem(Player player, PlayerData playerData) {
        ItemStack item = new ItemStack(Material.BOOK);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§b§lYOUR STATS");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lBLOCKS");
        long playerBlocks = playerData.getBlocksMined() != null ? playerData.getBlocksMined() : 0;
        lore.add("§7| §fTotal Mined: §b" + plugin.newFormatNumber(playerBlocks, player));
        lore.add("");
        
        lore.add("§7§lUPGRADE LEVELS");
        lore.add("§7| §6Mine: §f" + upgradeManager.getCurrentUpgradeLevel(playerData, MineBuddy.LocationType.MINE));
        lore.add("§7| §aFarmer: §f" + upgradeManager.getCurrentUpgradeLevel(playerData, MineBuddy.LocationType.FARMER_WARP));
        lore.add("§7| §5Dungeon: §f" + upgradeManager.getCurrentUpgradeLevel(playerData, MineBuddy.LocationType.DUNGEON));
        lore.add("");
        
        lore.add("§7§lTOTAL BONUSES");
        double mineBonus = (upgradeManager.calculateBonusMultiplier(playerData, MineBuddy.LocationType.MINE) - 1.0) * 100;
        double farmerBonus = (upgradeManager.calculateBonusMultiplier(playerData, MineBuddy.LocationType.FARMER_WARP) - 1.0) * 100;
        double dungeonBonus = (upgradeManager.calculateBonusMultiplier(playerData, MineBuddy.LocationType.DUNGEON) - 1.0) * 100;
        
        lore.add("§7| §6Mine: §f+" + String.format("%.0f", mineBonus) + "%");
        lore.add("§7| §aFarmer: §f+" + String.format("%.0f", farmerBonus) + "%");
        lore.add("§7| §5Dungeon: §f+" + String.format("%.0f", dungeonBonus) + "%");
        lore.add("");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * Gets the appropriate material for an upgrade item based on location type
     * 
     * @param locationType The location type
     * @return The material to use
     */
    private Material getUpgradeMaterial(MineBuddy.LocationType locationType) {
        switch (locationType) {
            case MINE:
                return Material.DIAMOND_PICKAXE;
            case FARMER_WARP:
                return Material.WHEAT;
            case DUNGEON:
                return Material.NETHER_STAR;
            default:
                return Material.STONE;
        }
    }
}
